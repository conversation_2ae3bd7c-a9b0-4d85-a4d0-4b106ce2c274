import { NextResponse } from 'next/server';
import { GoogleGenAI } from '@google/genai';

// Initialize the Gemini API client
const apiKey = process.env.GOOGLE_API_KEY;
const genAI = new GoogleGenAI({ apiKey });

// Helper function to fix common JSON issues
function fixJsonString(jsonStr) {
  // Fix common JSON syntax issues
  let fixed = jsonStr;

  // Fix trailing commas
  fixed = fixed.replace(/,(\s*[}\]])/g, '$1');

  // Fix missing commas between properties and objects
  fixed = fixed.replace(/"\s*\n\s*"/g, '",\n"');
  fixed = fixed.replace(/}(\s*)"([^"]+)":/g, '},\n"$2":');
  fixed = fixed.replace(/}(\s*)"/g, '},\n"');

  // Fix missing commas after string values
  fixed = fixed.replace(/"(\s*)"([^"]+)":/g, '",\n"$2":');

  // Fix unescaped quotes in strings
  fixed = fixed.replace(/:\s*"([^"\\]*)\\?([^"\\]*)\\?([^"]*)"(\s*[,}\]])/g, (_, p1, p2, p3, p4) => {
    const content = (p1 + p2 + p3).replace(/"/g, '\\"');
    return `: "${content}"${p4}`;
  });

  // Fix newlines in string values
  fixed = fixed.replace(/:\s*"([^"]*)\n([^"]*)"(\s*[,}])/g, ': "$1\\n$2"$3');

  // Fix single quotes to double quotes (but not inside strings)
  fixed = fixed.replace(/:\s*'([^']*)'/g, ': "$1"');

  // Fix missing quotes around property names
  fixed = fixed.replace(/([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:/g, '$1"$2":');

  // Fix boolean and null values that might be quoted
  fixed = fixed.replace(/:\s*"(true|false|null)"/g, ': $1');

  return fixed;
}

// Helper function to safely parse JSON from AI responses
function safeJsonParse(text, context = '') {
  try {
    // Clean the JSON text more thoroughly
    let cleanedText = text.replace(/```json\n?|\n?```/g, '').trim();

    // Remove control characters that break JSON parsing
    cleanedText = cleanedText.replace(/[\x00-\x1F\x7F-\x9F]/g, ' ');

    // Replace problematic characters that might appear in text
    cleanedText = cleanedText.replace(/[\u2018\u2019]/g, "'"); // Smart quotes to regular quotes
    cleanedText = cleanedText.replace(/[\u201C\u201D]/g, '"'); // Smart double quotes
    cleanedText = cleanedText.replace(/\u2026/g, '...'); // Ellipsis

    // Try to extract just the JSON object if there's text before or after it
    const jsonObjectMatch = cleanedText.match(/(\{[\s\S]*\})/);
    if (jsonObjectMatch && jsonObjectMatch[1]) {
      cleanedText = jsonObjectMatch[1];
    }

    // Apply JSON fixes
    cleanedText = fixJsonString(cleanedText);

    console.log(`${context} cleaned text:`, cleanedText.substring(0, 300) + '...');
    return JSON.parse(cleanedText);
  } catch (error) {
    console.error(`JSON parsing error in ${context}:`, error);
    console.error('Raw text:', text.substring(0, 800) + '...');

    // Try more aggressive fixing
    try {
      let aggressiveText = text.replace(/```json\n?|\n?```/g, '').trim();

      // Remove all control characters and replace with spaces
      aggressiveText = aggressiveText.replace(/[\x00-\x1F\x7F-\x9F]/g, ' ');

      // Extract JSON object
      const match = aggressiveText.match(/(\{[\s\S]*\})/);
      if (match) {
        let jsonText = match[1];

        // More aggressive JSON repair
        jsonText = fixJsonString(jsonText);

        // Try to fix incomplete JSON by validating structure
        try {
          // Find the position where JSON becomes invalid
          let braceCount = 0;
          let inString = false;
          let escaped = false;
          let lastValidPos = 0;

          for (let i = 0; i < jsonText.length; i++) {
            const char = jsonText[i];

            if (escaped) {
              escaped = false;
              continue;
            }

            if (char === '\\' && inString) {
              escaped = true;
              continue;
            }

            if (char === '"') {
              inString = !inString;
              continue;
            }

            if (!inString) {
              if (char === '{') {
                braceCount++;
              } else if (char === '}') {
                braceCount--;
                if (braceCount === 0) {
                  lastValidPos = i + 1;
                }
              }
            }
          }

          // If we have unmatched braces, truncate to last valid position
          if (braceCount > 0 && lastValidPos > 0) {
            jsonText = jsonText.substring(0, lastValidPos);
          } else if (braceCount > 0) {
            // Add missing closing braces
            jsonText += '}'.repeat(braceCount);
          }
        } catch (structureError) {
          console.error('Structure validation failed:', structureError);
        }

        console.log('Attempting aggressive repair...');
        console.log('Repaired JSON:', jsonText.substring(0, 300) + '...');
        return JSON.parse(jsonText);
      }
    } catch (aggressiveError) {
      console.error('Aggressive repair also failed:', aggressiveError);
    }

    throw error;
  }
}

export async function POST(request) {
  try {
    // Handle both FormData (with audio) and JSON (without audio)
    let whatIfPrompt = '';
    let audioTranscription = '';
    let detectedLanguage = 'en-US';

    const contentType = request.headers.get('content-type');

    if (contentType && contentType.includes('multipart/form-data')) {
      // Handle FormData with potential audio file
      const formData = await request.formData();
      whatIfPrompt = formData.get('whatIfPrompt');
      const audioFile = formData.get('audioFile');

      if (!whatIfPrompt) {
        return NextResponse.json(
          { error: 'What if prompt is required' },
          { status: 400 }
        );
      }

      // Process audio file if provided
      if (audioFile && audioFile.size > 0) {
        try {
          console.log('Processing audio file:', audioFile.name);

          // Create FormData for speech-to-text API
          const speechFormData = new FormData();
          speechFormData.append('audioFile', audioFile);

          // Call speech-to-text API
          const speechResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/speech-to-text`, {
            method: 'POST',
            body: speechFormData,
          });

          if (speechResponse.ok) {
            const speechResult = await speechResponse.json();
            audioTranscription = speechResult.transcription || '';
            detectedLanguage = speechResult.detectedLanguage || 'en-US';
            console.log('Audio transcription successful:', audioTranscription.substring(0, 100) + '...');
          } else {
            console.warn('Audio transcription failed, continuing without it');
          }
        } catch (audioError) {
          console.error('Error processing audio:', audioError);
          // Continue without audio transcription
        }
      }
    } else {
      // Handle JSON request (backward compatibility)
      const body = await request.json();
      whatIfPrompt = body.whatIfPrompt;

      if (!whatIfPrompt) {
        return NextResponse.json(
          { error: 'What if prompt is required' },
          { status: 400 }
        );
      }
    }

    if (!apiKey) {
      return NextResponse.json(
        { error: 'API key not configured' },
        { status: 500 }
      );
    }

    const model = 'gemini-2.0-flash-001';

    // Combine text input with audio transcription
    const combinedContext = audioTranscription
      ? `${whatIfPrompt}\n\nAdditional context from audio: ${audioTranscription}`
      : whatIfPrompt;

    // Detect if input is in Hindi
    const isHindiInput = /[\u0900-\u097F]/.test(combinedContext) || detectedLanguage === 'hi-IN';

    // Generate the story with new format - readable text instead of JSON
    const storyPrompt = `
You are a creative fiction writer specializing in interactive storytelling. Create an engaging "what if" story based on the user's scenario.

User's "What If" scenario: ${combinedContext}

${isHindiInput ?
  `भाषा निर्देश: इनपुट हिंदी में है, इसलिए कृपया सभी कंटेंट केवल हिंदी भाषा में जेनरेट करें। देवनागरी लिपि का उपयोग करें।` :
  `Language Instruction: Generate all content in English language.`
}

Create an interactive story in the following format:

**STORY TITLE**
[Create an engaging title for this story]

**OPENING SCENARIO**
[Write a compelling 300-400 word opening that sets up the alternate scenario. Make it vivid and immersive.]

**CHOICE POINT 1**
Now you must decide what happens next. Choose one of the following options:

A) [First choice option - 30-40 words describing the action]
B) [Second choice option - 30-40 words describing the action]
C) [Third choice option - 30-40 words describing the action]

**SCENARIO A: [Title for choice A]**
[300-400 word continuation if choice A is selected]

**CHOICE POINT 2A**
What happens next?

A1) [Choice option - 30-40 words]
A2) [Choice option - 30-40 words]
A3) [Choice option - 30-40 words]

**SCENARIO B: [Title for choice B]**
[300-400 word continuation if choice B is selected]

**CHOICE POINT 2B**
What happens next?

B1) [Choice option - 30-40 words]
B2) [Choice option - 30-40 words]
B3) [Choice option - 30-40 words]

**SCENARIO C: [Title for choice C]**
[300-400 word continuation if choice C is selected]

**CHOICE POINT 2C**
What happens next?

C1) [Choice option - 30-40 words]
C2) [Choice option - 30-40 words]
C3) [Choice option - 30-40 words]

Continue this pattern for one more level (scenarios A1, A2, A3, B1, B2, B3, C1, C2, C3) to create a rich branching narrative.

Make the story engaging, with meaningful choices that lead to different outcomes. Each scenario should feel complete while building toward the next decision point.`;

    const storyResult = await genAI.models.generateContent({
      model,
      contents: [
        { role: 'user', parts: [{ text: storyPrompt }] }
      ],
      config: {
        temperature: 0.7,
        topP: 0.9,
        topK: 40,
        maxOutputTokens: 4096, // Increased for longer story content
      },
    });

    let storyText = '';
    if (storyResult.candidates && storyResult.candidates.length > 0) {
      const candidate = storyResult.candidates[0];
      if (candidate.content && candidate.content.parts && candidate.content.parts.length > 0) {
        storyText = candidate.content.parts[0].text || '';
      }
    }

    if (!storyText) {
      return NextResponse.json(
        { error: 'Failed to generate story content' },
        { status: 500 }
      );
    }

    // Create the response with the generated story text
    const storyResponse = {
      success: true,
      storyText: storyText,
      detectedLanguage: detectedLanguage,
      hasAudioTranscription: !!audioTranscription,
      metadata: {
        whatIfPrompt: whatIfPrompt,
        audioTranscription: audioTranscription || null,
        generatedAt: new Date().toISOString()
      }
    };

    return NextResponse.json(storyResponse);

  } catch (error) {
    console.error('Error generating story tree:', error);
    return NextResponse.json(
      { error: 'Failed to generate story tree' },
      { status: 500 }
    );
  }
}

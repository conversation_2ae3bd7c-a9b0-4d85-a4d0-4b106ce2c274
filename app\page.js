 
'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { ChevronRight, Volume2, Brain, Book, Play, ArrowRight, Star, Zap, Users, Menu, X } from 'lucide-react';

export default function HomePage() {
  const [isVisible, setIsVisible] = useState({});
  const [activeStep, setActiveStep] = useState(0);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsVisible(prev => ({
              ...prev,
              [entry.target.id]: true
            }));
          }
        });
      },
      { threshold: 0.1 }
    );

    const sections = document.querySelectorAll('[data-animate]');
    sections.forEach((section) => observer.observe(section));

    return () => observer.disconnect();
  }, []);

  const features = [
    {
      icon: <Upload className="w-8 h-8" />,
      title: "Story Upload & Analysis",
      description: "Upload any existing story or narrative and our AI analyzes key plot points and character developments automatically."
    },
    {
      icon: <Brain className="w-8 h-8" />,
      title: "AI-Powered Timelines",
      description: "Google's Gemini AI creates compelling alternate timeline scenarios based on your original story's content."
    },
    {
      icon: <Book className="w-8 h-8" />,
      title: "Interactive Narratives",
      description: "Explore 'what-if' scenarios through engaging, branching storylines that reimagine your favorite tales."
    },
    {
      icon: <Play className="w-8 h-8" />,
      title: "Immersive Experience",
      description: "Experience alternate timelines through multiple formats including text, visuals, and interactive decision points."
    }
  ];

  const steps = [
    {
      number: "01",
      title: "Enter Book Details",
      description: "Start by entering details about any book or story you want to reimagine.",
      icon: <Book className="w-6 h-6" />
    },
    {
      number: "02",
      title: "AI Story Generation",
      description: "Our advanced AI creates alternate storylines based on your 'what-if' scenario.",
      icon: <Brain className="w-6 h-6" />
    },
    {
      number: "03",
      title: "Listen with TTS",
      description: "Experience your story with high-quality text-to-speech in multiple languages.",
      icon: <Volume2 className="w-6 h-6" />
    },
    {
      number: "04",
      title: "Navigate Choices",
      description: "Explore different story paths and endings through interactive audio storytelling.",
      icon: <Play className="w-6 h-6" />
    }
  ];

  const stats = [
    { number: "5K+", label: "Audio Stories Created" },
    { number: "25K+", label: "Hours Listened" },
    { number: "95%", label: "User Satisfaction" },
    { number: "2", label: "Languages Supported" }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveStep((prev) => (prev + 1) % steps.length);
    }, 3000);

    return () => clearInterval(interval);
  }, [steps.length]);

  return (
    <div className="min-h-screen bg-app-background text-app-text-primary">
      {/* Navigation */}
      <nav className="bg-app-surface border-b border-app-surface-light sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-2">
              <h1 className="font-slackey text-2xl md:text-3xl text-app-text-primary">What-if</h1>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              <a href="#about" className="text-app-text-secondary hover:text-app-text-primary transition-colors duration-200">
                About
              </a>
              <a href="#how-it-works" className="text-app-text-secondary hover:text-app-text-primary transition-colors duration-200">
                How It Works
              </a>
              <a href="#features" className="text-app-text-secondary hover:text-app-text-primary transition-colors duration-200">
                Features
              </a>
            </div>

            {/* Desktop Auth Buttons */}
            <div className="hidden md:flex items-center space-x-4">
              <Link
                href="/register"
                className="text-app-text-secondary hover:text-app-text-primary transition-colors duration-200"
              >
                Sign In
              </Link>
              <Link
                href="/auth"
                className="px-6 py-2 bg-app-accent text-app-text-primary rounded-md hover:bg-app-accent-hover transition-colors duration-200 font-medium"
              >
                Get Started
              </Link>
            </div>

            {/* Mobile Menu Button */}
            <button
              className="md:hidden text-app-text-primary p-2"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              aria-label="Toggle mobile menu"
            >
              {isMobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>

          {/* Mobile Menu */}
          {isMobileMenuOpen && (
            <div className="md:hidden mt-4 pb-4 border-t border-app-surface-light pt-4">
              <div className="flex flex-col space-y-4">
                <a
                  href="#about"
                  className="text-app-text-secondary hover:text-app-text-primary transition-colors duration-200 py-2"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  About
                </a>
                <a
                  href="#how-it-works"
                  className="text-app-text-secondary hover:text-app-text-primary transition-colors duration-200 py-2"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  How It Works
                </a>
                <a
                  href="#features"
                  className="text-app-text-secondary hover:text-app-text-primary transition-colors duration-200 py-2"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Features
                </a>
                <div className="flex flex-col space-y-3 pt-4 border-t border-app-surface-light">
                  <Link
                    href="/auth"
                    className="text-app-text-secondary hover:text-app-text-primary transition-colors duration-200 py-2"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    Sign In
                  </Link>
                  <Link
                    href="/auth"
                    className="px-6 py-3 bg-app-accent text-app-text-primary rounded-md hover:bg-app-accent-hover transition-colors duration-200 font-medium text-center"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    Get Started
                  </Link>
                </div>
              </div>
            </div>
          )}
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative overflow-hidden py-12 md:py-20 lg:py-32">
        <div className="absolute inset-0 bg-gradient-to-br from-app-accent/20 to-transparent"></div>

        <div className="container mx-auto px-4 relative">
          <div className="flex flex-col lg:flex-row items-center">
            {/* Hero Content */}
            <div className="lg:w-1/2 lg:pr-12 mb-8 lg:mb-0 text-center lg:text-left">
              <div className="space-y-6">
                <h1 className="font-libre text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold leading-tight">
                  Reimagine
                  <span className="block text-app-accent">Your Favorite</span>
                  Stories
                </h1>

                <p className="text-lg md:text-xl lg:text-2xl text-app-text-secondary leading-relaxed max-w-2xl mx-auto lg:mx-0">
                  Transform any existing story into interactive alternate timeline narratives powered by AI.
                  Explore &quot;what-if&quot; scenarios that reimagine beloved tales in entirely new ways.
                </p>

                <div className="flex flex-col sm:flex-row gap-4 pt-6 justify-center lg:justify-start">
                  <Link
                    href="/auth"
                    className="group px-6 md:px-8 py-3 md:py-4 bg-app-accent text-app-text-primary rounded-lg hover:bg-app-accent-hover transition-all duration-300 font-semibold text-base md:text-lg flex items-center justify-center"
                  >
                    Start Your Story
                    <ArrowRight className="ml-2 w-4 h-4 md:w-5 md:h-5 group-hover:translate-x-1 transition-transform duration-200" />
                  </Link>

                  <Link
                    href="#how-it-works"
                    className="px-6 md:px-8 py-3 md:py-4 border border-app-text-secondary text-app-text-secondary hover:text-app-text-primary hover:border-app-text-primary rounded-lg transition-all duration-300 font-semibold text-base md:text-lg text-center"
                  >
                    See How It Works
                  </Link>
                </div>

                {/* Stats - hidden on mobile, shown on larger screens */}
                <div className="hidden md:grid grid-cols-2 lg:grid-cols-4 gap-6 pt-12">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center lg:text-left">
                      <div className="text-2xl lg:text-3xl font-bold text-app-accent mb-1">{stat.number}</div>
                      <div className="text-sm text-app-text-secondary">{stat.label}</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Hero Image */}
            <div className="hidden lg:block lg:w-1/2 relative">
              <div className="relative w-full max-w-lg mx-auto">
                {/* Main coin mascot - responsive sizing */}
                <div className="relative w-64 h-64 md:w-80 md:h-80 lg:w-96 lg:h-96 mx-auto">
                  <Image
                    src="/what-if_ai_mascot.svg"
                    alt="What-if AI Mascot"
                    fill
                    className="object-contain"
                    priority
                  />
                </div>

                {/* Floating elements - hidden on mobile and small tablets */}
                 <div className="absolute top-10 left-10 w-16 h-16 bg-app-accent/20 rounded-full flex items-center justify-center backdrop-blur-sm animate-bounce">
                  <Brain className="w-8 h-8 text-app-accent" />
                </div>

                <div className="absolute top-20 right-10 w-12 h-12 bg-app-accent/20 rounded-full flex items-center justify-center backdrop-blur-sm animate-pulse">
                  <Book className="w-6 h-6 text-app-accent" />
                </div>

                <div className="absolute bottom-20 left-5 w-14 h-14 bg-app-accent/20 rounded-full flex items-center justify-center backdrop-blur-sm animate-bounce delay-1000">
                  <Star className="w-7 h-7 text-app-accent" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-20 bg-app-surface" data-animate>
        <div className="container mx-auto px-4">
          <div className={`text-center mb-16 transition-all duration-1000 ${isVisible.about ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
            <h2 className="font-libre text-4xl md:text-5xl font-bold mb-6">
              What is <span className="text-app-accent">What-if</span>?
            </h2>
            <p className="text-xl text-app-text-secondary max-w-3xl mx-auto leading-relaxed">
              What-if is an innovative AI-powered platform that transforms existing stories into interactive alternate timeline narratives.
              Using our proprietary technology and advanced text embedding technology, we create compelling alternate scenarios
              that reimagine your favorite tales in entirely new and engaging ways.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className={`group p-6 bg-app-background rounded-xl border border-app-surface-light hover:border-app-accent/50 transition-all duration-300 hover:transform hover:scale-105 ${
                  isVisible.about ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
                }`}
                style={{ transitionDelay: `${index * 150}ms` }}
              >
                <div className="text-app-accent mb-4 group-hover:scale-110 transition-transform duration-300">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold mb-3 group-hover:text-app-accent transition-colors duration-300">
                  {feature.title}
                </h3>
                <p className="text-app-text-secondary leading-relaxed">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section id="how-it-works" className="py-20" data-animate>
        <div className="container mx-auto px-4">
          <div className={`text-center mb-16 transition-all duration-1000 ${isVisible['how-it-works'] ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
            <h2 className="font-libre text-4xl md:text-5xl font-bold mb-6">
              How It <span className="text-app-accent">Works</span>
            </h2>
            <p className="text-xl text-app-text-secondary max-w-2xl mx-auto">
              Transform your existing stories into interactive alternate timelines in just four simple steps
            </p>
          </div>

          <div className="max-w-6xl mx-auto">
            <div className="relative">
              {/* Desktop connecting line - positioned behind all steps */}
              <div className="hidden lg:block absolute top-8 left-0 right-0 h-0.5 z-0">
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-app-accent/50 to-transparent"></div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-6 relative z-10">
                {steps.map((step, index) => (
                  <div
                    key={index}
                    className={`group relative transition-all duration-500 ${
                      isVisible['how-it-works'] ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
                    } ${activeStep === index ? 'transform scale-105' : ''}`}
                    style={{ transitionDelay: `${index * 200}ms` }}
                  >
                    {/* Step Number with background to cover the line */}
                    <div className={`relative w-16 h-16 rounded-full border-2 border-app-accent flex items-center justify-center mb-6 mx-auto transition-all duration-300 bg-[#0f0f0f] z-20 ${
                      activeStep === index ? 'bg-app-accent text-app-text-primary' : 'text-app-accent'
                    }`}>
                      <span className="font-bold text-lg">{step.number}</span>
                    </div>

                    {/* Step Content */}
                    <div className="text-center px-4">
                      <div className="flex justify-center mb-3">
                        <div className={`p-3 rounded-lg transition-colors duration-300 ${
                          activeStep === index ? 'bg-app-accent/20 text-app-accent' : 'bg-app-surface-light text-app-text-secondary'
                        }`}>
                          {step.icon}
                        </div>
                      </div>

                      <h3 className="text-lg md:text-xl font-semibold mb-3 group-hover:text-app-accent transition-colors duration-300">
                        {step.title}
                      </h3>

                      <p className="text-app-text-secondary leading-relaxed text-sm md:text-base">
                        {step.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* CTA Button */}
            <div className="text-center mt-16">
              <Link
                href="/auth"
                className="group inline-flex items-center px-8 py-4 bg-app-accent text-app-text-primary rounded-lg hover:bg-app-accent-hover transition-all duration-300 font-semibold text-lg"
              >
                Try It Now
                <ChevronRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-200" />
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features Showcase */}
      <section id="features" className="py-20 bg-app-surface" data-animate>
        <div className="container mx-auto px-4">
          <div className={`text-center mb-16 transition-all duration-1000 ${isVisible.features ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
            <h2 className="font-libre text-4xl md:text-5xl font-bold mb-6">
              Powerful <span className="text-app-accent">Features</span>
            </h2>
            <p className="text-xl text-app-text-secondary max-w-2xl mx-auto">
              Everything you need to transform existing stories into engaging alternate timeline narratives
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
            {/* Feature 1 */}
            <div className={`group p-6 md:p-8 bg-app-background rounded-xl border border-app-surface-light hover:border-app-accent/50 transition-all duration-500 hover:transform hover:scale-105 ${
              isVisible.features ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}>
              <div className="flex flex-col sm:flex-row sm:items-center mb-6">
                <div className="p-3 bg-app-accent/20 rounded-lg mb-4 sm:mb-0 sm:mr-4 w-fit">
                  <Zap className="w-6 h-6 md:w-8 md:h-8 text-app-accent" />
                </div>
                <h3 className="text-xl md:text-2xl font-semibold">Lightning Fast AI</h3>
              </div>
              <p className="text-app-text-secondary leading-relaxed mb-4">
                Powered by Google&apos;s Gemini AI, our platform generates compelling alternate scenarios in seconds, not hours.
              </p>
              <ul className="space-y-2 text-sm text-app-text-secondary">
                <li className="flex items-center">
                  <div className="w-1.5 h-1.5 bg-app-accent rounded-full mr-3"></div>
                  Advanced text embedding analysis
                </li>
                <li className="flex items-center">
                  <div className="w-1.5 h-1.5 bg-app-accent rounded-full mr-3"></div>
                  Real-time scenario generation
                </li>
                <li className="flex items-center">
                  <div className="w-1.5 h-1.5 bg-app-accent rounded-full mr-3"></div>
                  Intelligent content chunking
                </li>
              </ul>
            </div>

            {/* Feature 2 */}
            <div className={`group p-6 md:p-8 bg-app-background rounded-xl border border-app-surface-light hover:border-app-accent/50 transition-all duration-500 hover:transform hover:scale-105 ${
              isVisible.features ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`} style={{ transitionDelay: '200ms' }}>
              <div className="flex flex-col sm:flex-row sm:items-center mb-6">
                <div className="p-3 bg-app-accent/20 rounded-lg mb-4 sm:mb-0 sm:mr-4 w-fit">
                  <Users className="w-6 h-6 md:w-8 md:h-8 text-app-accent" />
                </div>
                <h3 className="text-xl md:text-2xl font-semibold">User-Friendly Design</h3>
              </div>
              <p className="text-app-text-secondary leading-relaxed mb-4">
                Simple, intuitive interface that makes complex AI technology accessible to everyone, regardless of technical expertise.
              </p>
              <ul className="space-y-2 text-sm text-app-text-secondary">
                <li className="flex items-center">
                  <div className="w-1.5 h-1.5 bg-app-accent rounded-full mr-3"></div>
                  Drag-and-drop file uploads
                </li>
                <li className="flex items-center">
                  <div className="w-1.5 h-1.5 bg-app-accent rounded-full mr-3"></div>
                  Interactive story navigation
                </li>
                <li className="flex items-center">
                  <div className="w-1.5 h-1.5 bg-app-accent rounded-full mr-3"></div>
                  Mobile-responsive design
                </li>
              </ul>
            </div>

            {/* Feature 3 */}
            <div className={`group p-6 md:p-8 bg-app-background rounded-xl border border-app-surface-light hover:border-app-accent/50 transition-all duration-500 hover:transform hover:scale-105 ${
              isVisible.features ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`} style={{ transitionDelay: '400ms' }}>
              <div className="flex flex-col sm:flex-row sm:items-center mb-6">
                <div className="p-3 bg-app-accent/20 rounded-lg mb-4 sm:mb-0 sm:mr-4 w-fit">
                  <Book className="w-6 h-6 md:w-8 md:h-8 text-app-accent" />
                </div>
                <h3 className="text-xl md:text-2xl font-semibold">Rich Storytelling</h3>
              </div>
              <p className="text-app-text-secondary leading-relaxed mb-4">
                Create immersive, branching narratives that adapt to your specific questions and exploration needs.
              </p>
              <ul className="space-y-2 text-sm text-app-text-secondary">
                <li className="flex items-center">
                  <div className="w-1.5 h-1.5 bg-app-accent rounded-full mr-3"></div>
                  Interactive decision points
                </li>
                <li className="flex items-center">
                  <div className="w-1.5 h-1.5 bg-app-accent rounded-full mr-3"></div>
                  Multiple story paths
                </li>
                <li className="flex items-center">
                  <div className="w-1.5 h-1.5 bg-app-accent rounded-full mr-3"></div>
                  Visual scene generation
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-b from-app-accent-hover to-[#6B21A8]">
        <div className="container mx-auto px-4 text-center">
          <h2 className="font-libre text-4xl md:text-5xl font-bold mb-6 text-white">
            Ready to Transform Your Favorite Stories?
          </h2>
          <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
            Join thousands of users who are already exploring alternate timelines through AI-powered storytelling
          </p>
          <Link
            href="/auth"
            className="inline-flex items-center px-8 py-4 bg-white text-app-accent rounded-lg hover:bg-gray-100 transition-all duration-300 font-semibold text-lg"
          >
            Start Your Journey
            <ArrowRight className="ml-2 w-5 h-5" />
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-app-surface border-t border-app-surface-light py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Brand */}
            <div className="md:col-span-2 lg:col-span-1">
              <h3 className="font-slackey text-2xl text-app-text-primary mb-4">What-if</h3>
              <p className="text-app-text-secondary leading-relaxed">
                Transforming storytelling through AI-powered interactive alternate timeline narratives.
              </p>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="font-semibold text-app-text-primary mb-4">Quick Links</h4>
              <ul className="space-y-2">
                <li><a href="#about" className="text-app-text-secondary hover:text-app-text-primary transition-colors duration-200">About</a></li>
                <li><a href="#how-it-works" className="text-app-text-secondary hover:text-app-text-primary transition-colors duration-200">How It Works</a></li>
                <li><a href="#features" className="text-app-text-secondary hover:text-app-text-primary transition-colors duration-200">Features</a></li>
                <li><Link href="/auth" className="text-app-text-secondary hover:text-app-text-primary transition-colors duration-200">Get Started</Link></li>
              </ul>
            </div>

            {/* Support */}
            <div>
              <h4 className="font-semibold text-app-text-primary mb-4">Support</h4>
              <ul className="space-y-2">
                <li><a href="#" className="text-app-text-secondary hover:text-app-text-primary transition-colors duration-200">Help Center</a></li>
                <li><a href="#" className="text-app-text-secondary hover:text-app-text-primary transition-colors duration-200">Contact Us</a></li>
                <li><a href="#" className="text-app-text-secondary hover:text-app-text-primary transition-colors duration-200">Privacy Policy</a></li>
                <li><a href="#" className="text-app-text-secondary hover:text-app-text-primary transition-colors duration-200">Terms of Service</a></li>
              </ul>
            </div>

            {/* Connect */}
            <div>
              <h4 className="font-semibold text-app-text-primary mb-4">Connect</h4>
              <ul className="space-y-2">
                <li><a href="#" className="text-app-text-secondary hover:text-app-text-primary transition-colors duration-200">Twitter</a></li>
                <li><a href="#" className="text-app-text-secondary hover:text-app-text-primary transition-colors duration-200">LinkedIn</a></li>
                <li><a href="#" className="text-app-text-secondary hover:text-app-text-primary transition-colors duration-200">GitHub</a></li>
                <li><a href="#" className="text-app-text-secondary hover:text-app-text-primary transition-colors duration-200">Discord</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-app-surface-light mt-8 pt-8 text-center">
            <p className="text-app-text-secondary">
              © 2024 What-if. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
} 

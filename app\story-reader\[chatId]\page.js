'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { auth } from '@/lib/firebase';
import { ThemedButton } from '@/app/theme/ThemeProvider';
import StoryContent from '@/app/components/StoryContent';
import { ArrowLeft } from 'lucide-react';

export default function StoryReaderPage() {
  const params = useParams();
  const router = useRouter();
  const [user, setUser] = useState(null);
  const [authLoading, setAuthLoading] = useState(true);
  
  const [storyData, setStoryData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [fetchError, setFetchError] = useState(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);

  const chatId = params?.chatId;

  // Check authentication state
  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged((currentUser) => {
      setUser(currentUser);
      setAuthLoading(false);
    });

    return () => unsubscribe();
  }, []);

  // Fetch story data from Firestore
  useEffect(() => {
    const fetchStoryData = async () => {
      if (!chatId) {
        setFetchError('No chat ID provided');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        const response = await fetch(`/api/chats/${chatId}/storyData`);
        
        if (!response.ok) {
          if (response.status === 404) {
            setFetchError('Story not found');
          } else {
            setFetchError('Failed to load story data');
          }
          setIsLoading(false);
          return;
        }

        const data = await response.json();
        setStoryData(data);
        setFetchError(null);
      } catch (error) {
        console.error('Error fetching story data:', error);
        setFetchError('Failed to load story data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchStoryData();
  }, [chatId]);

  // Navigate between story sections
  const handleNavigate = (index) => {
    if (index >= 0 && index < storyData.questions.length) {
      setCurrentQuestionIndex(index);
    }
  };

  // Redirect to login if not authenticated
  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-app-pattern">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#8B5CF6]"></div>
      </div>
    );
  }

  if (!authLoading && !user) {
    router.push('/register');
    return null;
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-app-pattern">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#8B5CF6] mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your story...</p>
        </div>
      </div>
    );
  }

  if (fetchError) {
    return (
      <div className="min-h-screen bg-app-pattern flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-lg p-6 sm:p-8 max-w-md w-full text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-2xl">⚠️</span>
          </div>
          <h1 className="text-xl sm:text-2xl font-bold text-red-600 mb-4">Error Loading Story</h1>
          <p className="text-gray-700 mb-6">{fetchError}</p>
          <div className="space-y-3">
            <ThemedButton onClick={() => window.location.reload()} variant="primary">
              Try Again
            </ThemedButton>
            <ThemedButton onClick={() => router.back()} variant="secondary">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Go Back
            </ThemedButton>
          </div>
        </div>
      </div>
    );
  }

  if (!storyData || !storyData.questions || storyData.questions.length === 0) {
    return (
      <div className="min-h-screen bg-app-pattern flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">No Story Found</h1>
          <p className="text-gray-600 mb-6">This story doesn't have any content yet.</p>
          <ThemedButton onClick={() => router.back()}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Go Back
          </ThemedButton>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-app-pattern">
      <div className="container mx-auto px-4 py-6 sm:py-8 max-w-5xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-6 sm:mb-8">
          <ThemedButton
            variant="ghost"
            onClick={() => router.back()}
            className="flex items-center"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            <span className="hidden sm:inline">Back</span>
          </ThemedButton>

          <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-800 text-center">
            Story Reader
          </h1>

          <div className="w-16 sm:w-20"></div> {/* Spacer for centering */}
        </div>

        {/* Story Content */}
        <div className="bg-white rounded-lg shadow-lg p-4 sm:p-6 lg:p-8">
          <StoryContent
            storyData={storyData}
            currentQuestionIndex={currentQuestionIndex}
            onNavigate={handleNavigate}
          />
        </div>

        {/* Footer */}
        <div className="mt-8 text-center text-sm text-gray-500">
          <p>Use the audio controls to listen to the story content</p>
        </div>
      </div>
    </div>
  );
}

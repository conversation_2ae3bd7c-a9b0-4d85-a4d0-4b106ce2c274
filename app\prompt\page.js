'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { onAuthStateChanged } from 'firebase/auth';
import { auth } from '@/lib/firebase';
import { ArrowLeft, BookOpen, Sparkles, Loader2 } from 'lucide-react';
import Link from 'next/link';

export default function PromptPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const chatId = searchParams.get('chatId');

  const [user, setUser] = useState(null);
  const [authLoading, setAuthLoading] = useState(true);
  const [formData, setFormData] = useState({
    whatIfPrompt: '',
    audioFile: null
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // Auth listener
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {
      setUser(currentUser);
      setAuthLoading(false);
      if (!currentUser) {
        router.push('/register');
      }
    });

    return () => unsubscribe();
  }, [router]);

  // Redirect if no chatId
  useEffect(() => {
    if (!chatId) {
      router.push('/dashboard');
    }
  }, [chatId, router]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    setFormData(prev => ({
      ...prev,
      audioFile: file
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.whatIfPrompt) {
      setError('Please provide a "What if" scenario');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Create FormData for file upload
      const submitData = new FormData();
      submitData.append('whatIfPrompt', formData.whatIfPrompt);
      if (formData.audioFile) {
        submitData.append('audioFile', formData.audioFile);
      }

      // Generate story tree
      const response = await fetch('/api/generate-story-tree', {
        method: 'POST',
        body: submitData, // Use FormData instead of JSON
      });

      if (!response.ok) {
        throw new Error('Failed to generate story');
      }

      const storyData = await response.json();

      // Save story data to the chat
      const saveResponse = await fetch(`/api/chats/${chatId}/storyData`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(storyData),
      });

      if (!saveResponse.ok) {
        throw new Error('Failed to save story data');
      }

      // Update chat with simplified info
      await fetch(`/api/chats/${chatId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: `What If Story`,
          bookInfo: { whatIfPrompt: formData.whatIfPrompt }
        }),
      });

      // Redirect to story reader
      router.push(`/story-reader/${chatId}`);

    } catch (error) {
      console.error('Error generating story:', error);
      setError(error.message || 'Failed to generate story. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (authLoading) {
    return (
      <div className="min-h-screen bg-app-pattern flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin text-app-accent" />
      </div>
    );
  }

  if (!chatId || !user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-app-pattern p-4">
      <div className="max-w-2xl mx-auto">
        {/* Header */}
        <div className="flex items-center mb-8">
          <Link
            href={`/chat/${chatId}`}
            className="flex items-center text-app-text-secondary hover:text-app-text-primary transition-colors mr-4"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back to Chat
          </Link>
        </div>

        {/* Main Form */}
        <div className="bg-app-surface rounded-xl shadow-lg p-6 md:p-8">
          <div className="text-center mb-8">
            <div className="flex justify-center mb-4">
              <div className="p-3 bg-app-accent/20 rounded-full">
                <BookOpen className="w-8 h-8 text-app-accent" />
              </div>
            </div>
            <h1 className="text-3xl font-bold text-app-text-primary mb-2">
              Create Your What-If Story
            </h1>
            <p className="text-app-text-secondary">
              Describe your "what if" scenario and optionally upload an audio file for additional context
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* What If Prompt */}
            <div>
              <label htmlFor="whatIfPrompt" className="block text-sm font-medium text-app-text-primary mb-2">
                What if... *
              </label>
              <textarea
                id="whatIfPrompt"
                name="whatIfPrompt"
                value={formData.whatIfPrompt}
                onChange={handleInputChange}
                placeholder="Describe your alternate scenario idea. For example: 'What if Harry Potter had been sorted into Slytherin instead of Gryffindor?' or 'What if Romeo and Juliet had met in a different city?'"
                rows={6}
                className="w-full px-4 py-3 bg-app-background border border-app-surface-light rounded-lg focus:ring-2 focus:ring-app-accent focus:border-transparent text-app-text-primary placeholder-app-text-secondary resize-none"
                disabled={isLoading}
              />
            </div>

            {/* Audio File Upload */}
            <div>
              <label htmlFor="audioFile" className="block text-sm font-medium text-app-text-primary mb-2">
                Audio File (Optional)
              </label>
              <div className="relative">
                <input
                  type="file"
                  id="audioFile"
                  name="audioFile"
                  accept=".mp3,.wav,.m4a,.aac,.ogg,.flac"
                  onChange={handleFileChange}
                  className="w-full px-4 py-3 bg-app-background border border-app-surface-light rounded-lg focus:ring-2 focus:ring-app-accent focus:border-transparent text-app-text-primary file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-app-accent file:text-app-text-primary hover:file:bg-app-accent-hover"
                  disabled={isLoading}
                />
              </div>
              <p className="text-xs text-app-text-secondary mt-2">
                Upload an audio file to provide additional context for your story. Supported formats: MP3, WAV, M4A, AAC, OGG, FLAC
              </p>
            </div>

            {/* Error Message */}
            {error && (
              <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                <p className="text-red-400 text-sm">{error}</p>
              </div>
            )}

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading}
              className="w-full flex items-center justify-center px-6 py-3 bg-app-accent text-app-text-primary rounded-lg hover:bg-app-accent-hover transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                  Generating Your Story...
                </>
              ) : (
                <>
                  <Sparkles className="w-5 h-5 mr-2" />
                  Generate Story
                </>
              )}
            </button>
          </form>
        </div>
      </div>
    </div>
  );
}

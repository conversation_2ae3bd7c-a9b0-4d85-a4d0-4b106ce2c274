'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuthContext } from '@/lib/auth-context';
import { ArrowLeft, BookOpen, Sparkles, Loader2 } from 'lucide-react';
import Link from 'next/link';

export default function PromptPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuthContext();
  const chatId = searchParams.get('chatId');

  const [formData, setFormData] = useState({
    bookTitle: '',
    author: '',
    changeLocation: '',
    whatIfPrompt: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // Redirect if no chatId
  useEffect(() => {
    if (!chatId) {
      router.push('/dashboard');
    }
  }, [chatId, router]);

  // Redirect if not authenticated
  useEffect(() => {
    if (!user) {
      router.push('/auth');
    }
  }, [user, router]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.bookTitle || !formData.author || !formData.changeLocation || !formData.whatIfPrompt) {
      setError('Please fill in all fields');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Generate story tree
      const response = await fetch('/api/generate-story-tree', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error('Failed to generate story');
      }

      const storyData = await response.json();

      // Save story data to the chat
      const saveResponse = await fetch(`/api/chats/${chatId}/storyData`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(storyData),
      });

      if (!saveResponse.ok) {
        throw new Error('Failed to save story data');
      }

      // Update chat with book info
      await fetch(`/api/chats/${chatId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: `${formData.bookTitle} - What If`,
          bookInfo: formData
        }),
      });

      // Redirect to story reader
      router.push(`/story-reader/${chatId}`);

    } catch (error) {
      console.error('Error generating story:', error);
      setError(error.message || 'Failed to generate story. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (!chatId || !user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-app-pattern p-4">
      <div className="max-w-2xl mx-auto">
        {/* Header */}
        <div className="flex items-center mb-8">
          <Link
            href={`/chat/${chatId}`}
            className="flex items-center text-app-text-secondary hover:text-app-text-primary transition-colors mr-4"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back to Chat
          </Link>
        </div>

        {/* Main Form */}
        <div className="bg-app-surface rounded-xl shadow-lg p-6 md:p-8">
          <div className="text-center mb-8">
            <div className="flex justify-center mb-4">
              <div className="p-3 bg-app-accent/20 rounded-full">
                <BookOpen className="w-8 h-8 text-app-accent" />
              </div>
            </div>
            <h1 className="text-3xl font-bold text-app-text-primary mb-2">
              Create Your What-If Story
            </h1>
            <p className="text-app-text-secondary">
              Tell us about the book and your alternate scenario idea
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Book Title */}
            <div>
              <label htmlFor="bookTitle" className="block text-sm font-medium text-app-text-primary mb-2">
                Book Title *
              </label>
              <input
                type="text"
                id="bookTitle"
                name="bookTitle"
                value={formData.bookTitle}
                onChange={handleInputChange}
                placeholder="e.g., Harry Potter and the Philosopher's Stone"
                className="w-full px-4 py-3 bg-app-background border border-app-surface-light rounded-lg focus:ring-2 focus:ring-app-accent focus:border-transparent text-app-text-primary placeholder-app-text-secondary"
                disabled={isLoading}
              />
            </div>

            {/* Author */}
            <div>
              <label htmlFor="author" className="block text-sm font-medium text-app-text-primary mb-2">
                Author *
              </label>
              <input
                type="text"
                id="author"
                name="author"
                value={formData.author}
                onChange={handleInputChange}
                placeholder="e.g., J.K. Rowling"
                className="w-full px-4 py-3 bg-app-background border border-app-surface-light rounded-lg focus:ring-2 focus:ring-app-accent focus:border-transparent text-app-text-primary placeholder-app-text-secondary"
                disabled={isLoading}
              />
            </div>

            {/* Change Location */}
            <div>
              <label htmlFor="changeLocation" className="block text-sm font-medium text-app-text-primary mb-2">
                Where in the story should the change begin? *
              </label>
              <input
                type="text"
                id="changeLocation"
                name="changeLocation"
                value={formData.changeLocation}
                onChange={handleInputChange}
                placeholder="e.g., When Harry first meets Hagrid"
                className="w-full px-4 py-3 bg-app-background border border-app-surface-light rounded-lg focus:ring-2 focus:ring-app-accent focus:border-transparent text-app-text-primary placeholder-app-text-secondary"
                disabled={isLoading}
              />
            </div>

            {/* What If Prompt */}
            <div>
              <label htmlFor="whatIfPrompt" className="block text-sm font-medium text-app-text-primary mb-2">
                What if... *
              </label>
              <textarea
                id="whatIfPrompt"
                name="whatIfPrompt"
                value={formData.whatIfPrompt}
                onChange={handleInputChange}
                placeholder="e.g., What if Harry had been sorted into Slytherin instead of Gryffindor?"
                rows={4}
                className="w-full px-4 py-3 bg-app-background border border-app-surface-light rounded-lg focus:ring-2 focus:ring-app-accent focus:border-transparent text-app-text-primary placeholder-app-text-secondary resize-none"
                disabled={isLoading}
              />
            </div>

            {/* Error Message */}
            {error && (
              <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                <p className="text-red-400 text-sm">{error}</p>
              </div>
            )}

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading}
              className="w-full flex items-center justify-center px-6 py-3 bg-app-accent text-app-text-primary rounded-lg hover:bg-app-accent-hover transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                  Generating Your Story...
                </>
              ) : (
                <>
                  <Sparkles className="w-5 h-5 mr-2" />
                  Generate Story
                </>
              )}
            </button>
          </form>
        </div>
      </div>
    </div>
  );
}
